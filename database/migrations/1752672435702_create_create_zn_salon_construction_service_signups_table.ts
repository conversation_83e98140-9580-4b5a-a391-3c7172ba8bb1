import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'zn_salon_construction_service_signups'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('id').primary()

      // Salon Owner Information
      table.string('fullName').notNullable()
      table.string('businessName').notNullable()
      table.text('salonAddress').notNullable()
      table.string('phoneNumber').notNullable()
      table.string('emailAddress').notNullable()

      // Service Interest (stored as JSON array)
      table.json('serviceInterest').notNullable()

      // Timeline & Budget
      table.date('preferredStartDate').nullable()
      table.string('budgetRange').notNullable()

      // Additional Notes / Vision
      table.text('additionalNotes').nullable()

      // Consent & Signature
      table.boolean('consentConfirmed').defaultTo(false)
      table.string('signature').nullable()
      table.date('signatureDate').nullable()

      // Status and PDF
      table.enum('status', ['pending', 'approved', 'rejected']).defaultTo('pending')
      table.uuid('pdfFileId').references('id').inTable('zn_medias').onDelete('SET NULL').nullable()

      // Timestamps
      table.datetime('createdAt').defaultTo(this.now())
      table.datetime('updatedAt').defaultTo(this.now())
      table.datetime('deletedAt').nullable().index()
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}

import env from '#start/env'
import sharp from 'sharp'
import {
  S3<PERSON>lient,
  PutObjectCommand,
  ObjectCannedACL,
  AbortMultipartUploadCommand,
  CompleteMultipartUploadCommand,
  UploadPartCommand,
  CreateMultipartUploadCommand,
  HeadObjectCommand,
  GetObjectCommand,
  DeleteObjectCommand,
} from '@aws-sdk/client-s3'
import { randomUUID } from 'crypto'
import { Readable } from 'node:stream'

export class AmazonS3StorageService {
  private bucketName: string | undefined
  private region: string | undefined
  private accessKeyId: string | undefined
  private secretAccessKey: string | undefined
  public fullUrl: string | undefined

  private readonly s3Client: S3Client

  constructor() {
    this.bucketName = env.get('AWS_S3_BUCKET_NAME')
    this.region = env.get('AWS_S3_REGION')
    this.accessKeyId = env.get('AWS_S3_ACCESS_KEY_ID')
    this.secretAccessKey = env.get('AWS_S3_SECRET_ACCESS_KEY')
    this.fullUrl = `https://s3.amazonaws.com/${this.bucketName}/`

    this.s3Client = new S3Client({
      region: this.region,
      credentials: {
        accessKeyId: this.accessKeyId!,
        secretAccessKey: this.secretAccessKey!,
      },
    })
  }

  async compressImage(file: Buffer): Promise<Buffer> {
    try {
      return await sharp(file).webp({ minSize: true, quality: 70 }).toBuffer()
    } catch (error) {
      return file
    }
  }

  async uploadImages(
    files: {
      fileName: string
      buffer: Buffer
      mimeType: string
      extname: string
    }[]
  ): Promise<{
    uploadedFiles: {
      fileId: string
      fileKey: string
      filePath: string
      fileUrl: string
      fileType: string
    }[]
    failedFiles: { error: string; fileName: string }[]
  }> {
    const uploadPromises = files.map(async (file) => {
      try {
        const fileBuffer = file.buffer
        const fileType = this.getFileType(file.mimeType)
        const compressedImage = await this.compressImage(fileBuffer)

        const now = new Date()
        const year = now.getFullYear()
        const month = (now.getMonth() + 1).toString().padStart(2, '0')
        const day = now.getDate().toString().padStart(2, '0')

        let fileKey = `${year}/${month}/${day}/${Date.now()}-${randomUUID()}${file.extname}`
        if (fileType == 'image') {
          fileKey = `${year}/${month}/${day}/${Date.now()}-${randomUUID()}.webp`
        }

        const uploadParams = {
          Bucket: this.bucketName!,
          Body: compressedImage,
          Key: fileKey,
          ContentType: file.mimeType,
          ACL: ObjectCannedACL.public_read,
        }
        const command = new PutObjectCommand(uploadParams)
        const uploadResult = await this.s3Client.send(command)

        const fileUrl = `${this.fullUrl}${fileKey}`

        return {
          fileId: randomUUID(),
          fileKey: fileKey,
          filePath: uploadResult.$metadata.requestId,
          fileUrl: fileUrl,
          fileType,
        }
      } catch (error) {
        console.log('Upload error:', error)
        return { error: error.message, fileName: file.fileName }
      }
    })

    const results = await Promise.allSettled(uploadPromises)

    const uploadedFiles = results
      .filter((result) => result.status === 'fulfilled')
      .map((result) => (result as PromiseFulfilledResult<any>).value)

    const failedFiles = results
      .filter(
        (result) =>
          result.status === 'rejected' || (result as PromiseFulfilledResult<any>).value?.error
      )
      .map((result) =>
        result.status === 'rejected'
          ? (result as PromiseRejectedResult).reason
          : (result as PromiseFulfilledResult<any>).value
      )

    return {
      uploadedFiles,
      failedFiles,
    }
  }

  async createMultipartUpload(fileName: string, mimeType: string, filePrefix: string) {
    const command = new CreateMultipartUploadCommand({
      Bucket: this.bucketName,
      Key: filePrefix + fileName,
      ContentType: mimeType,
      ACL: ObjectCannedACL.public_read,
    })
    const response = await this.s3Client.send(command)
    return response.UploadId
  }

  async uploadChunk(
    uploadId: string,
    fileName: string,
    partNumber: number,
    buffer: Buffer,
    filePrefix: string
  ) {
    const command = new UploadPartCommand({
      Bucket: this.bucketName,
      Key: filePrefix + fileName,
      UploadId: uploadId,
      PartNumber: partNumber,
      Body: buffer,
    })
    const response = await this.s3Client.send(command)
    return response.ETag
  }

  async completeMultipartUpload(
    uploadId: string,
    fileName: string,
    parts: { ETag: string; PartNumber: number }[],
    filePrefix: string
  ) {
    const command = new CompleteMultipartUploadCommand({
      Bucket: this.bucketName,
      Key: filePrefix + fileName,
      UploadId: uploadId,
      MultipartUpload: {
        Parts: parts,
      },
    })
    return await this.s3Client.send(command)
  }

  async abortMultipartUpload(uploadId: string, fileName: string, filePrefix: string) {
    const command = new AbortMultipartUploadCommand({
      Bucket: this.bucketName,
      Key: filePrefix + fileName,
      UploadId: uploadId,
    })
    await this.s3Client.send(command)
  }

  getFullUrl() {
    if (env.get('AWS_S3_CDN_URL')) {
      return env.get('AWS_S3_CDN_URL')
    }
    return this.fullUrl
  }

  getFileType(mime: any) {
    if (mime.startsWith('image/')) {
      return 'image'
    } else if (mime.startsWith('video/')) {
      return 'video'
    } else if (
      mime === 'application/pdf' ||
      mime === 'application/msword' ||
      mime === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ) {
      return 'doc'
    } else {
      return 'unknown'
    }
  }

  async uploadReports(
    files: {
      fileName: string
      buffer: Buffer
      mimeType: string
      extname: string
    }[]
  ): Promise<{
    uploadedFiles: { fileId: string; fileKey: string; filePath: string; fileUrl: string }[]
    failedFiles: { error: string; fileName: string }[]
  }> {
    const uploadPromises = files.map(async (file) => {
      try {
        const fileBuffer = file.buffer

        const now = new Date()
        const year = now.getFullYear()
        const month = (now.getMonth() + 1).toString().padStart(2, '0')
        const day = now.getDate().toString().padStart(2, '0')

        const fileKey = `reports/${year}/${month}/${day}/${Date.now()}-${randomUUID()}${file.extname}`

        const uploadParams = {
          Bucket: this.bucketName!,
          Body: fileBuffer,
          Key: fileKey,
          ContentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          ACL: ObjectCannedACL.public_read,
        }
        const command = new PutObjectCommand(uploadParams)
        const uploadResult = await this.s3Client.send(command)

        const fileUrl = `${this.fullUrl}${fileKey}`

        return {
          fileId: randomUUID(),
          fileKey: fileKey,
          filePath: uploadResult.$metadata.requestId,
          fileUrl: fileUrl,
        }
      } catch (error) {
        console.log('Upload error:', error)
        return { error: error.message, fileName: file.fileName }
      }
    })

    const results = await Promise.allSettled(uploadPromises)

    const uploadedFiles = results
      .filter((result) => result.status === 'fulfilled')
      .map((result) => (result as PromiseFulfilledResult<any>).value)

    const failedFiles = results
      .filter(
        (result) =>
          result.status === 'rejected' || (result as PromiseFulfilledResult<any>).value?.error
      )
      .map((result) =>
        result.status === 'rejected'
          ? (result as PromiseRejectedResult).reason
          : (result as PromiseFulfilledResult<any>).value
      )

    return {
      uploadedFiles,
      failedFiles,
    }
  }

  async streamVideo(fileKey: string, range?: string) {
    try {
      const bucket = this.bucketName

      // Get video metadata
      const headParams = { Bucket: bucket, Key: fileKey }
      const headObject = await this.s3Client.send(new HeadObjectCommand(headParams))
      const videoSize = headObject.ContentLength

      if (!videoSize) {
        throw new Error('Video file not found in S3')
      }

      if (!range) {
        throw new Error('Requires Range header')
      }

      // Parse range properly (e.g., "bytes=0-999")
      const rangeMatch = range.match(/bytes=(\d+)-(\d*)/)
      if (!rangeMatch) {
        throw new Error('Invalid Range header')
      }

      const start = parseInt(rangeMatch[1], 10)
      let end = rangeMatch[2]
        ? parseInt(rangeMatch[2], 10)
        : Math.min(start + 10 ** 6, videoSize - 1)

      // Ensure `end` doesn't exceed `videoSize - 1`
      end = Math.min(end, videoSize - 1)

      if (start >= videoSize || end >= videoSize) {
        throw new Error('Requested range not satisfiable')
      }

      const contentLength = end - start + 1

      // Fetch video stream from S3
      const streamParams = {
        Bucket: bucket,
        Key: fileKey,
        Range: `bytes=${start}-${end}`,
      }

      const videoStream = await this.s3Client.send(new GetObjectCommand(streamParams))

      if (!videoStream.Body || !(videoStream.Body instanceof Readable)) {
        throw new Error('Failed to fetch video stream from S3')
      }

      return { videoStream: videoStream.Body, contentLength, start, end, videoSize }
    } catch (error) {
      console.error('Error streaming video:', error)
      throw error
    }
  }

  async uploadPdf(file: { fileName: string; buffer: Buffer; mimeType: string }): Promise<{
    fileId: string
    fileKey: string
    filePath: string
    fileUrl: string
  }> {
    try {
      const now = new Date()
      const year = now.getFullYear()
      const month = (now.getMonth() + 1).toString().padStart(2, '0')
      const day = now.getDate().toString().padStart(2, '0')

      const fileKey = `pdfs/${year}/${month}/${day}/${Date.now()}-${randomUUID()}-${file.fileName}`

      const uploadParams = {
        Bucket: this.bucketName!,
        Body: file.buffer,
        Key: fileKey,
        ContentType: file.mimeType,
        ACL: ObjectCannedACL.public_read,
      }
      const command = new PutObjectCommand(uploadParams)
      const uploadResult = await this.s3Client.send(command)

      const fileUrl = `${this.fullUrl}${fileKey}`

      return {
        fileId: randomUUID(),
        fileKey: fileKey,
        filePath: uploadResult.$metadata.requestId || '',
        fileUrl: fileUrl,
      }
    } catch (error) {
      console.log('Upload PDF error:', error)
      throw new Error(`Failed to upload PDF: ${error.message}`)
    }
  }

  async deleteFile(fileKey: string) {
    const data = {
      Bucket: this.bucketName!,
      Key: fileKey,
    }
    const command = new DeleteObjectCommand(data)
    const response = await this.s3Client.send(command)
    return response
  }
}

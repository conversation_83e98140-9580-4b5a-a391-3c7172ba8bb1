/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import router from '@adonisjs/core/services/router'

const SalonConstructionServiceController = () => import('#controllers/salon_construction_service_controller')

export default function salonConstructionServiceRoutes() {
  router
    .group(() => {
      router.post('/', [SalonConstructionServiceController, 'store'])
      router.get('/:id', [SalonConstructionServiceController, 'show'])
      router.get('/:id/pdf', [SalonConstructionServiceController, 'downloadPdf'])
    })
    .prefix('salon-construction-service')
}

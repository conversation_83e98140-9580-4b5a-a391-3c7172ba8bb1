import router from '@adonisjs/core/services/router'

import adminRoutes from '../../../admin/route/v1/index.js'
import collectionsRoutes from './collections.js'
import gptRoutes from './app/gpt.js'
import appRoutes from './app/index.js'
import orderRoutes from './order.js'
import productRoutes from './product.js'
import trackingRoutes from './app/tracking.js'
import campaignRoutes from './campaign.js'
import commentRoutes from './comment.js'
import homeLayoutsRoutes from './home_layout.js'
import mediaRoutes from './media.js'
import postRoutes from './post.js'
import postCategoryRoutes from './post_category.js'
import routesV1 from './routes.js'
import storeRoutes from './store.js'
import translationRoutes from './translation.js'
import newsfeedRoutes from './newsfeed.js'
import userRoutes from './user.js'
import socketRoutes from './socket.js'
import chatRoutes from './chat_router.js'
import affiliationRoutes from './affiliation.js'
import eventRoutes from './events.js'
import chatBotRoutes from './chat_bot_router.js'
import receptionistRoutes from "#start/route/v1/app/receptionist";
import referralRoutes from './referral_router.js'
import vendorRoutes from './vendor.js'

export default function v1Routes() {
  router
    .group(() => {
      adminRoutes()

      appRoutes()

      campaignRoutes()
      mediaRoutes()
      postCategoryRoutes()
      postRoutes()
      storeRoutes()
      trackingRoutes()
      translationRoutes()
      commentRoutes()
      gptRoutes()
      homeLayoutsRoutes()
      newsfeedRoutes()
      orderRoutes()
      productRoutes()
      collectionsRoutes()
      userRoutes()
      socketRoutes()
      chatRoutes()
      affiliationRoutes()
      eventRoutes()
      chatBotRoutes()
      receptionistRoutes()
      referralRoutes()
      vendorRoutes()

      routesV1()
    })
    .prefix('v1')
}

import type { HttpContext } from '@adonisjs/core/http'
import VendorService from "../../../services/vendors/vendor_service.js";
// import { ModelPaginatorContract } from '@adonisjs/lucid/types/model';
import ZnUser from "#models/zn_user";
import { ShopifyService } from '#services/shopify/shopify_service';
import ZnVendorOrder, { EVendorOrderStatus } from '#models/zn_vendor_order';
import db from '@adonisjs/lucid/services/db';
import ZnOrderFulfillment from '#models/zn_order_fulfillment';

export default class VendorOrdersController {
  private vendorService: VendorService

  constructor() {
    this.vendorService = new VendorService()
  }

  /**
   * Display a list of resource
   */
  async index({ auth, request, response }: HttpContext) {
    //Get vendor id from login
    const user = auth.getUserOrFail() as ZnUser
    const { page, limit, search, status } = request.qs()

    const query = ZnVendorOrder.query()
      .where({ vendorId: user.vendorId || '' })
      .preload('orderDetails', (detailQuery) => {
        detailQuery.preload('variant', (variantQuery) => {
          variantQuery
            .preload('image')
            .preload('product')
        })
      })
      .withCount('orderDetails')
      .preload('order', (orderQuery) => {
        orderQuery
          .preload('user')
          .preload('shipping')
      })
      .preload('fulfillment')
      .orderBy('createdAt', 'desc')

    if (search) {
      query.whereHas('order', (orderQuery) => {
        orderQuery.whereILike('name', `%${search}%`)
      })
    }

    if (status) {
      query.where({ status })
    }

    const result = await query.paginate(page, limit)

    const orderStatusCounts = await ZnVendorOrder.query()
      .where({ vendorId: user.vendorId || '' })
      .groupBy('status')
      .select('status', db.raw('COUNT(*) as count'))

    const statusCounts: any = {}
    orderStatusCounts.map(statusCount => {
      statusCounts[statusCount.status] = statusCount.$extras.count
      statusCounts[''] = (statusCounts[''] || 0) + statusCount.$extras.count
    })

    return response.ok({
      meta: { ...result.toJSON().meta, statusCounts },
      data: result.toJSON().data,
    })
  }


  /**
   * Show individual record
   */
  async show({ auth, params, response }: HttpContext) {
    const user = auth.getUserOrFail() as ZnUser
    const orderId = params.id

    const order = await ZnVendorOrder.query()
      .where({
        id: orderId,
        vendorId: user.vendorId || "",
      })
      .preload('orderDetails', (detailQuery) => {
        detailQuery.preload('variant', (variantQuery) => {
          variantQuery
            .preload('image')
            .preload('product')
        })
      })
      .preload('order', (orderQuery) => {
        orderQuery
          .preload('user')
          .preload('shipping')
          .preload('billing')
      })
      .preload('fulfillment')
      .first()

    if (order) {
      return response.ok(order)
    }

    return response.notFound("Order not found")
  }

  /**
   * Handle form submission for the edit action
   */
  async update({ auth, params, request, response }: HttpContext) {
    const user = auth.getUserOrFail() as ZnUser
    const orderId = params.id

    const order = await ZnVendorOrder.query()
      .where({
        id: orderId,
        vendorId: user.vendorId || '',
      })
      .preload('orderDetails', (detailQuery) => {
        detailQuery.preload('variant', (variantQuery) => {
          variantQuery.preload('image')
        })
      })
      .preload('order', (orderQuery) => {
        orderQuery
          .preload('user')
          .preload('shipping')
          .preload('billing')
      })
      .preload('fulfillment')
      .first()

    const { status, trackingInfo, shippingAddress } = request.body()

    if (!order) {
      return response.notFound('Order Not Found')
    }

    const shopifyService = new ShopifyService()
    if (status == EVendorOrderStatus.CANCELLED) {
      const res = await shopifyService.cancelOrder({ orderId: `${order.order?.shopifyId}`, note: 'Vendor Cancel' })
      console.log(res);
    }

    if (shippingAddress) {
      await shopifyService.updateOrder({
        id: order.order?.shopifyId,
        shippingAddress: {
          firstName: shippingAddress.firstName,
          lastName: shippingAddress.lastName,
          phone: shippingAddress.phone,
          company: shippingAddress.company,
          address1: shippingAddress.address1,
          address2: shippingAddress.address2,
          city: shippingAddress.city,
          zip: shippingAddress.zip,
          provinceCode: shippingAddress.provinceCode,
          countryCode: shippingAddress.countryCode,
        }
      })
    }

    let shopifyFulfillment, dbFulfillment
    if (trackingInfo) {
      if (order.fulfillmentId) {
        shopifyFulfillment = await shopifyService.updateFulfillmentTrackingInfo(
          order.fulfillment.shopifyFulfillmentId,
          {
            company: trackingInfo?.trackingCompany,
            numbers: trackingInfo?.trackings?.map((track: any) => track.number),
            urls: trackingInfo?.trackings?.map((track: any) => track.url),
          }
        )

      } else {
        const res = await shopifyService.fetchOrdersWithIds([order.order.shopifyId])

        const openFulfillmentOrder = res.orders[0].fulfillmentOrders.nodes.find((fulOrd: any) => fulOrd.status == 'OPEN')

        if (openFulfillmentOrder) {
          const vendorFulfillmentOrderLineItems = []
          for (const item of openFulfillmentOrder.lineItems.nodes) {
            if (order.orderDetails.find(details => details.shopifyId == item.lineItem.id)) {
              vendorFulfillmentOrderLineItems.push({ id: item.id, quantity: item.totalQuantity })
            }
          }

          shopifyFulfillment = await shopifyService.createFulfillment({
            lineItemsByFulfillmentOrder: [
              {
                fulfillmentOrderId: openFulfillmentOrder.id,
                fulfillmentOrderLineItems: vendorFulfillmentOrderLineItems
              }
            ],
            trackingInfo: {
              company: trackingInfo?.trackingCompany,
              numbers: trackingInfo?.trackings?.map((track: any) => track.number),
              urls: trackingInfo?.trackings?.map((track: any) => track.url),
            }
          })


        }
      }

      dbFulfillment = await ZnOrderFulfillment.updateOrCreate(
        { shopifyFulfillmentId: shopifyFulfillment.id },
        {
          shopifyFulfillmentId: shopifyFulfillment.id,
          orderId: order.orderId,
          status: shopifyFulfillment.status?.toLowerCase(),
          trackingCompany: shopifyFulfillment.trackingInfo[0]?.company,
          trackingNumber: shopifyFulfillment.trackingInfo[0]?.number,
          trackingUrl: shopifyFulfillment.trackingInfo[0]?.url,
          trackingNumbers: shopifyFulfillment.trackingInfo.map((track: any) => track.number).join(','),
          trackingUrls: shopifyFulfillment.trackingInfo.map((track: any) => track.url).join(','),
        }
      )
    }

    order.status = status || order.status
    order.fulfillmentId = dbFulfillment?.id || order.fulfillmentId

    await order.save()

    return response.ok(order)
  }

  /**
   * Delete record
   */
  async destroy({ params, response }: HttpContext) {
    const orderId = params.id
    const order = await this.vendorService.getOrderById(orderId)
    if (order) {
      return order.softDelete()
    }

    return response.notFound("Order not found")
  }
}

import VendorService from "#services/vendors/vendor_service";
import { HttpContext } from "@adonisjs/core/http";
import VendorPaymentService from "#services/vendors/vendor_payment_service";

export default class VendorPaymentController {
  private vendorService: VendorService;
  private vendorPaymentService: VendorPaymentService;

  constructor() {
    this.vendorService = new VendorService();
    this.vendorPaymentService = new VendorPaymentService();
  }

  async index({ auth, response }: HttpContext) {
    try {
      // @ts-ignore
      const user = auth.getUserOrFail().serialize() as ZnUser
      const vendor = await this.vendorService.getVendorByUserId(user.id);
      const earnings = await this.vendorPaymentService.getAllPaymentsByVendor(vendor.id);
      return response.ok(earnings);
    } catch (error) {
      console.log(error);
      return response.badRequest(error);
    }
  }

  async show({ auth, params, response }: HttpContext) {
    try {
      // @ts-ignore
      const user = auth.getUserOrFail().serialize() as ZnUser

      const paymentId = params.id;
      if (!paymentId) {
        return response.badRequest('Payment ID is required.');
      }

      const payment = await this.vendorPaymentService.getPaymentById(paymentId);

      return response.ok(payment);
    } catch (error) {
      console.log(error);
      return response.badRequest(error);
    }
  }
}
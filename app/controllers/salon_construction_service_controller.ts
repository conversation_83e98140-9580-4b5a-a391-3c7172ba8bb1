import type { HttpContext } from '@adonisjs/core/http'
import ZnSalonConstructionServiceSignup from '#models/zn_salon_construction_service_signup'
import { createSalonConstructionServiceSignupValidator } from '#validators/salon_construction_service/salon_construction_service_validator'
import { SalonConstructionServicePdfGenerationService } from '#services/salon_construction_service/pdf_generation_service'
import { SALON_CONSTRUCTION_SERVICE_TEXT, ESalonConstructionServiceStatus } from '../constants/salon_construction_service.js'
import { DateTime } from 'luxon'

export default class SalonConstructionServiceController {
  private pdfService: SalonConstructionServicePdfGenerationService

  constructor() {
    this.pdfService = new SalonConstructionServicePdfGenerationService()
  }

  /**
   * @store
   * @tag Salon Construction Service
   * @summary Submit salon construction service signup form
   * @description Submit salon construction service signup form with all required information
   * @requestBody <ZnSalonConstructionServiceSignup>.only(fullName, businessName, salonAddress, phoneNumber, emailAddress, serviceInterest, preferredStartDate, budgetRange, additionalNotes, consentConfirmed, signature, signatureDate)
   * @responseBody 201 - {"success":true,"message":"Salon construction service signup submitted successfully","data":<ZnSalonConstructionServiceSignup>} - Success
   * @responseBody 400 - {"success":false,"message":"Validation failed","errors":[]} - Validation Error
   * @responseBody 500 - {"success":false,"message":"Failed to submit salon construction service signup"} - Internal Server Error
   */
  async store({ request, response }: HttpContext) {
    try {
      const data = request.all()
      const payload = await createSalonConstructionServiceSignupValidator.validate(data)

      // Create signup record
      const signup = await ZnSalonConstructionServiceSignup.create({
        ...payload,
        status: ESalonConstructionServiceStatus.PENDING,
        signatureDate: payload.signatureDate || DateTime.now()
      })

      // Generate PDF in background (don't wait for it to complete)
      this.generatePdfAsync(signup).catch((error) => {
        console.error('Failed to generate PDF for signup:', signup.id, error)
      })

      return response.created({
        success: true,
        message: SALON_CONSTRUCTION_SERVICE_TEXT.SIGNUP_SUCCESS,
        data: signup
      })
    } catch (error) {
      console.error('Error creating salon construction service signup:', error)
      
      if (error.messages) {
        return response.badRequest({
          success: false,
          message: SALON_CONSTRUCTION_SERVICE_TEXT.VALIDATION_ERROR,
          errors: error.messages
        })
      }

      return response.internalServerError({
        success: false,
        message: SALON_CONSTRUCTION_SERVICE_TEXT.SIGNUP_ERROR
      })
    }
  }

  /**
   * @show
   * @tag Salon Construction Service
   * @summary Get salon construction service signup by ID
   * @paramPath id - Signup ID - @type(string) @required
   * @responseBody 200 - {"success":true,"data":<ZnSalonConstructionServiceSignup>} - Success
   * @responseBody 404 - {"success":false,"message":"Salon construction service signup not found"} - Not Found
   */
  async show({ params, response }: HttpContext) {
    try {
      const signup = await ZnSalonConstructionServiceSignup.query()
        .where('id', params.id)
        .preload('pdfFile')
        .first()

      if (!signup) {
        return response.notFound({
          success: false,
          message: SALON_CONSTRUCTION_SERVICE_TEXT.NOT_FOUND
        })
      }

      return response.ok({
        success: true,
        data: signup
      })
    } catch (error) {
      console.error('Error fetching salon construction service signup:', error)
      return response.internalServerError({
        success: false,
        message: 'Failed to fetch signup'
      })
    }
  }

  /**
   * @downloadPdf
   * @tag Salon Construction Service
   * @summary Download PDF for salon construction service signup
   * @paramPath id - Signup ID - @type(string) @required
   * @responseBody 200 - PDF file - Success
   * @responseBody 404 - {"success":false,"message":"PDF not found"} - Not Found
   */
  async downloadPdf({ params, response }: HttpContext) {
    try {
      const signup = await ZnSalonConstructionServiceSignup.query()
        .where('id', params.id)
        .preload('pdfFile')
        .first()

      if (!signup || !signup.pdfFile) {
        return response.notFound({
          success: false,
          message: 'PDF not found'
        })
      }

      // Redirect to S3 URL
      return response.redirect(signup.pdfFile.url)
    } catch (error) {
      console.error('Error downloading PDF:', error)
      return response.internalServerError({
        success: false,
        message: 'Failed to download PDF'
      })
    }
  }

  /**
   * Generate PDF asynchronously
   */
  private async generatePdfAsync(signup: ZnSalonConstructionServiceSignup): Promise<void> {
    try {
      const pdfMedia = await this.pdfService.generatePdf(signup)
      
      // Update signup with PDF file ID
      signup.pdfFileId = pdfMedia.id
      await signup.save()
    } catch (error) {
      console.error('Failed to generate PDF:', error)
      throw error
    }
  }
}

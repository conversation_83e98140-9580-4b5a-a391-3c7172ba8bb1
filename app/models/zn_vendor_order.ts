import AppModel from '#models/app_model'
import { afterCreate, belongsTo, column, hasMany } from '@adonisjs/lucid/orm'
import { DateTime } from 'luxon';
import ZnOrder from './zn_order.js';
import type { BelongsTo, HasMany } from '@adonisjs/lucid/types/relations';
import ZnVendor from './zn_vendor.js';
import ZnOrderFulfillment from './zn_order_fulfillment.js';
import ZnOrderDetail from './zn_order_detail.js';
import mail from '@adonisjs/mail/services/main';
import VendorNewOrderNotification from '#mails/orders/vendor_new_order_notification';

export enum EVendorOrderStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  READY_TO_SHIP = 'readyToShip',
  IN_TRANSIT = 'inTransit',
  DELIVERED = 'delivered',
  RTO = 'rto',
  HELD = 'held',
  CANCELLED = 'cancelled',
}

export default class ZnVendorOrder extends AppModel {
  static table = 'zn_vendor_orders';

  @column({ columnName: 'status' })
  declare status: EVendorOrderStatus

  @column.dateTime({ columnName: 'cancelledAt' })
  declare cancelledAt: DateTime

  @column.dateTime({ columnName: 'closedAt' })
  declare closedAt: DateTime

  @column({
    columnName: 'subtotalPrice',
    consume: (value: string) => parseFloat(value),
  })
  declare subtotalPrice: number

  @column({
    columnName: 'totalTax',
    consume: (value: string) => parseFloat(value),
  })
  declare totalTax: number

  @column({
    columnName: 'totalDiscounts',
    consume: (value: string) => parseFloat(value),
  })
  declare totalDiscounts: number

  @column({
    columnName: 'totalShipping',
    consume: (value: string) => parseFloat(value),
  })
  declare totalShipping: number

  @column({
    columnName: 'totalPrice',
    consume: (value: string) => parseFloat(value),
  })
  declare totalPrice: number

  @column({
    columnName: 'currentTotalPrice',
    consume: (value: string) => parseFloat(value),
  })
  declare currentTotalPrice: number

  @column({ columnName: 'note' })
  declare note: string

  @column({ columnName: 'orderId' })
  declare orderId: string

  @column({ columnName: 'vendorId' })
  declare vendorId: string

  @column({ columnName: 'fulfillmentId' })
  declare fulfillmentId: string

  @belongsTo(() => ZnOrder, {
    foreignKey: 'orderId'
  })
  declare order: BelongsTo<typeof ZnOrder>;

  @belongsTo(() => ZnVendor, {
    foreignKey: 'vendorId'
  })
  declare vendor: BelongsTo<typeof ZnVendor>;

  @belongsTo(() => ZnOrderFulfillment, {
    foreignKey: 'fulfillmentId'
  })
  declare fulfillment: BelongsTo<typeof ZnOrderFulfillment>;

  @hasMany(() => ZnOrderDetail, {
    foreignKey: 'vendorOrderId',
  })
  declare orderDetails: HasMany<typeof ZnOrderDetail>

  @afterCreate()
  static async sendNotification(order: ZnVendorOrder) {
    console.log('vendor order created', order);

    await order.load('vendor')
    await order.load('order')
    await order.load('orderDetails')

    await mail.send(
      new VendorNewOrderNotification(
        '<EMAIL>',
        order,
      )
    )
      .then(() => {
        console.log(`Mail sent for ${order.order?.name} to vendor ${order.vendor?.companyName}`);
      })
      .catch((error) => {
        console.error('Error when sending email', error);
      })
  }

  serializeExtras() {
    return {
      orderDetailsCount: this.$extras.orderDetails_count,
    }
  }
}

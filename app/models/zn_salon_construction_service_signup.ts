import { belongsTo, column } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import { DateTime } from 'luxon'
import {
  ESalonConstructionServiceStatus,
  EServiceInterest,
} from '../constants/salon_construction_service.js'
import AppModel from './app_model.js'
import ZnMedia from './zn_media.js'

export default class ZnSalonConstructionServiceSignup extends AppModel {
  static table = 'zn_salon_construction_service_signups'

  @column({ columnName: 'fullName' })
  declare fullName: string

  @column({ columnName: 'businessName' })
  declare businessName: string

  @column({ columnName: 'salonAddress' })
  declare salonAddress: string

  @column({ columnName: 'phoneNumber' })
  declare phoneNumber: string

  @column({ columnName: 'emailAddress' })
  declare emailAddress: string

  @column({
    columnName: 'serviceInterest',
    prepare: (value: EServiceInterest[]) => JSON.stringify(value),
    consume: (value: string | EServiceInterest[]) => {
      if (Array.isArray(value)) return value
      if (typeof value === 'string') {
        try {
          return JSON.parse(value)
        } catch {
          return []
        }
      }
      return []
    },
  })
  declare serviceInterest: EServiceInterest[]

  @column.date({ columnName: 'preferredStartDate' })
  declare preferredStartDate: DateTime | null

  @column({ columnName: 'budgetRange' })
  declare budgetRange: string

  @column({ columnName: 'additionalNotes' })
  declare additionalNotes: string | null

  @column({ columnName: 'consentConfirmed' })
  declare consentConfirmed: boolean

  @column({ columnName: 'signature' })
  declare signature: string | null

  @column.date({ columnName: 'signatureDate' })
  declare signatureDate: DateTime | null

  @column({ columnName: 'status' })
  declare status: ESalonConstructionServiceStatus

  @column({ columnName: 'pdfFileId' })
  declare pdfFileId: string | null

  @belongsTo(() => ZnMedia, {
    foreignKey: 'pdfFileId',
  })
  declare pdfFile: BelongsTo<typeof ZnMedia>

  // Helper method to get formatted service interests
  get formattedServiceInterests(): string[] {
    const labels = {
      [EServiceInterest.FULL_RENOVATION]: 'Full Renovation',
      [EServiceInterest.PARTIAL_REMODEL]: 'Partial Remodel (e.g. chairs, counters, flooring)',
      [EServiceInterest.NEW_CONSTRUCTION]: 'New Construction',
      [EServiceInterest.ELECTRICAL_PLUMBING]: 'Electrical / Plumbing Updates',
      [EServiceInterest.INTERIOR_DESIGN]: 'Interior Design Services',
    }

    return this.serviceInterest.map((interest) => labels[interest] || interest)
  }

  // Helper method to check if approved
  get isApproved(): boolean {
    return this.status === ESalonConstructionServiceStatus.APPROVED
  }

  // Helper method to check if rejected
  get isRejected(): boolean {
    return this.status === ESalonConstructionServiceStatus.REJECTED
  }

  // Helper method to check if pending
  get isPending(): boolean {
    return this.status === ESalonConstructionServiceStatus.PENDING
  }
}

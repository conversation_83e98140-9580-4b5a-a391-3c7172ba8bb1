import AppMail from '#mails/app_mail';
import ZnVendorOrder from '#models/zn_vendor_order';

export default class VendorNewOrderNotification extends AppMail {
    constructor(
        private email: string,
        private order: ZnVendorOrder,
    ) {
        super()
    }

    /**
     * The "prepare" method is called automatically when
     * the email is sent or queued.
     */
    prepare() {
        this.message
            .subject(`New Order: ${this.order.order?.name}`)
            .htmlView('mails/orders/vendor_order', {
                serverDomain: this.baseUrl,
                order: this.order,
            })
            .to(this.email)
    }
}

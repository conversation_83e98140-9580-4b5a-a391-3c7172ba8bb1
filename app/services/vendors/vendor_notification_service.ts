import AppMail from "#mails/app_mail";
import VendorRegistrationApprovedNotification from "#mails/vendors/vendor_registration_approved_notification";
import VendorRegistrationConfirmNotification from "#mails/vendors/vendor_registration_confirm_notification";
import VendorRegistrationRejectedNotification from "#mails/vendors/vendor_registration_rejected_notification";
import ZnVendor from "#models/zn_vendor";
import logger from "@adonisjs/core/services/logger";
import mail from "@adonisjs/mail/services/main";

export default class VendorNotificationService {
  async sendRegistrationConfirmationNotification(vendor: ZnVendor) {
    await this.sendEmail(new VendorRegistrationConfirmNotification(vendor.companyName, vendor.email));
  }

  async sendRegistrationApprovedNotification(vendor: ZnVendor) {
    await this.sendEmail(new VendorRegistrationApprovedNotification(vendor));
  }

  async sendRegistrationRejectedNotification(vendor: ZnVendor) {
    await this.sendEmail(new VendorRegistrationRejectedNotification(vendor.companyName, vendor.email, vendor.rejectionReason ?? ''));
  }

  private async sendEmail(emailHandler: AppMail) {
    await mail
      .send(emailHandler)
      .then(() => {
        logger.info(`${Object.getPrototypeOf(emailHandler).constructor.name} has been sent successfully`);
      })
      .catch((error) => {
        console.error('Error when sending email', error);
      })
  }
}
import edge from 'edge.js'
import puppeteer from 'puppeteer'
import { AmazonS3StorageService } from '../../../services/aws/s3/aws-s3.service.js'
import ZnMedia from '#models/zn_media'
import { MEDIA_TYPE } from '../../constants/media.js'
import type ZnSalonConstructionServiceSignup from '#models/zn_salon_construction_service_signup'
import { DateTime } from 'luxon'

export class SalonConstructionServicePdfGenerationService {
  private s3Service: AmazonS3StorageService

  constructor() {
    this.s3Service = new AmazonS3StorageService()
  }

  /**
   * Generate PDF from salon construction service signup data
   */
  async generatePdf(signup: ZnSalonConstructionServiceSignup): Promise<ZnMedia> {
    try {
      // Prepare data for template
      const templateData = {
        fullName: signup.fullName,
        businessName: signup.businessName,
        salonAddress: signup.salonAddress,
        phoneNumber: signup.phoneNumber,
        emailAddress: signup.emailAddress,
        serviceInterest: signup.serviceInterest,
        preferredStartDate: signup.preferredStartDate?.toFormat('yyyy-MM-dd') || '',
        budgetRange: signup.budgetRange,
        additionalNotes: signup.additionalNotes,
        signature: signup.signature,
        signatureDate:
          signup.signatureDate?.toFormat('yyyy-MM-dd') || DateTime.now().toFormat('yyyy-MM-dd'),
        consentConfirmed: signup.consentConfirmed,
      }

      // Render HTML template
      const html = await edge.render('pdf/salon_construction_service_signup', {
        data: templateData,
      })

      // Generate PDF using Puppeteer
      const browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      })

      const page = await browser.newPage()
      await page.setContent(html, { waitUntil: 'networkidle0' })

      // Generate PDF buffer
      const pdfBuffer = await page.pdf({
        format: 'A4',
        printBackground: true,
        margin: {
          top: '20px',
          right: '20px',
          bottom: '20px',
          left: '20px',
        },
      })

      await browser.close()

      // Upload PDF to S3
      const now = new Date()
      const year = now.getFullYear()
      const month = (now.getMonth() + 1).toString().padStart(2, '0')
      const day = now.getDate().toString().padStart(2, '0')

      const fileName = `salon-construction-signup-${signup.id}-${Date.now()}.pdf`
      const fileKey = `${year}/${month}/${day}/${fileName}`

      const uploadResult = await this.s3Service.uploadPdf({
        fileName,
        buffer: pdfBuffer,
        mimeType: 'application/pdf',
      })

      if (!uploadResult.fileKey) {
        throw new Error('Failed to upload PDF to S3')
      }

      // Create ZnMedia record
      const media = await ZnMedia.create({
        fileKey: uploadResult.fileKey,
        url: uploadResult.fileUrl,
        type: MEDIA_TYPE.DOCUMENT,
        resourceId: signup.id,
        sourceFrom: 'salon_construction_service',
      })

      return media
    } catch (error) {
      console.error('Error generating PDF:', error)
      throw new Error('Failed to generate PDF')
    }
  }

  /**
   * Generate PDF preview (for testing)
   */
  async generatePdfPreview(data: any): Promise<Buffer> {
    try {
      const html = await edge.render('pdf/salon_construction_service_signup', {
        data,
      })

      const browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      })

      const page = await browser.newPage()
      await page.setContent(html, { waitUntil: 'networkidle0' })

      const pdfBuffer = await page.pdf({
        format: 'A4',
        printBackground: true,
        margin: {
          top: '20px',
          right: '20px',
          bottom: '20px',
          left: '20px',
        },
      })

      await browser.close()
      return pdfBuffer
    } catch (error) {
      console.error('Error generating PDF preview:', error)
      throw new Error('Failed to generate PDF preview')
    }
  }
}

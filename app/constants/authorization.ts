export enum RESOURCE {
  ADMIN = 'admin',
  USER = 'user',
  ROLE = 'role',
  CAMPAIGN = 'campaign',
  POST = 'post',
  POST_CATEGORY = 'postCategory',
  STORE = 'store',
  CLAIM_STORE_REQUEST = 'claimStoreRequest',
  BANNER = 'banner',
  DRAWER_MENU = 'drawerMenu',
  CONTACT_INFORMATION = 'contactInformation',
  NAIL_SYSTEM = 'nailSystem',
  CANCEL_REASON = 'cancelReason',
  COLLECTION = 'collection',
  PRODUCT = 'product',
  REWARD_POINT = 'rewardPoint',
  ORDER = 'order',
  COUPON = 'coupon',
  MARKETING = 'marketing',

  DASHBOARD = 'dashboard',
  ZURNO_POST = 'zurnoPost',
  TOP_CATEGORIES = 'topCategories',
  PRODUCTS_CATEGORIES = 'productsCategories',
  GIFT = 'gift',
  STREAM_POST = 'streamPost',
  AFFILIATION = 'affiliation',
  SHOP_CATEGORY = 'shopCategory',
  SHOP_COLLECTION = 'shopCollection',
  EVENT = 'event',

  REPORT = 'report',
  SALE_REPORT = 'saleReport',
  ORDER_REPORT = 'orderReport',
  INVENTORY_REPORT = 'inventoryReport',
  BARCODE_REPORT = 'barcodeReport',
  SHIPPING_CHECKOUT_REPORT = 'shippingCheckoutReport',

  VENDOR = 'vendor',
  SALON_CONSTRUCTION_SERVICE = 'salonConstructionService',

  DASHBOARD_CLASSIFIED = 'dashboardClassified',
  DASHBOARD_ECOMERCE = 'dashboardEcomerce',
  DASHBOARD_ANALYTIC = 'dashboardAnalytic',
}

export enum ACTION {
  CREATE = 'create',
  READ = 'read',
  UPDATE = 'update',
  DELETE = 'delete',

  UPDATE_SELF = 'update.self',

  ACTIVE = 'update.active',
  DEACTIVE = 'update.deactive',

  APPROVE = 'approve',
  REJECT = 'reject',

  EXPORT = 'export',

  DELETE_SELF = 'delete.self',

  ACCESS_TRANSLATION = 'accessTranslation',

  CHECK_IN = 'checkIn',
  ADD_REGISTRATION = 'addRegistration',
  VIEW_REGISTRATION = 'viewRegistration',
  DELETE_REGISTRATION = 'deleteRegistration',

  VIEW_DASHBOARD_CLASSIFIED_STATISTIC = 'view.dashboard.classified.statistic',
  VIEW_DASHBOARD_CLASSIFIED_POST_SUMMARY = 'view.dashboard.classified.postSummary',
  VIEW_DASHBOARD_CLASSIFIED_STORE_SUMMARY = 'view.dashboard.classified.storeSummary',
  VIEW_DASHBOARD_CLASSIFIED_MONTHLY_POST_COUNT = 'view.dashboard.classified.monthlyPostCount',
  VIEW_DASHBOARD_CLASSIFIED_TOP_CUSTOMER_BY_POST = 'view.dashboard.classified.topCustomerByPost',
  VIEW_DASHBOARD_CLASSIFIED_RECENT_POST = 'view.dashboard.classified.recentPost',

  VIEW_DASHBOARD_ECOMERCE_STATISTIC = 'view.dashboard.ecomerce.statistic',
  VIEW_DASHBOARD_ECOMERCE_ORDER_SUMMARY = 'view.dashboard.ecomerce.orderSummary',
  VIEW_DASHBOARD_ECOMERCE_TOP_SALE_COUNTRY = 'view.dashboard.ecomerce.topSaleCountry',
  VIEW_DASHBOARD_ECOMERCE_TOP_CUSTOMER = 'view.dashboard.ecomerce.topCustomer',
  VIEW_DASHBOARD_ECOMERCE_MONTHLY_SALE_COUNT = 'view.dashboard.ecomerce.monthlySaleCount',
  VIEW_DASHBAORD_ECOMERCE_TOP_SELLING_PRODUCT = 'view.dashboard.ecomerce.topSellingProduct',
  VIEW_DASHBOARD_ECOMERCE_RECENT_ORDER = 'view.dashboard.ecomerce.recentOrder',

  VIEW_DASHBOARD_ANALYTIC_TOP_SELLING_PRODUCT = 'view.dashboard.analytic.topSellingProduct',
  VIEW_DASHBOARD_ANALYTIC_MOST_VIEWED_PRODUCT = 'view.dashboard.analytic.mostViewedProduct',
  VIEW_DASHBOARD_ANALYTIC_TOP_FAVORITE_PRODUCT = 'view.dashboard.analytic.topFavoriteProduct',
  VIEW_DASHBOARD_ANALYTIC_HIGHEST_RATED_PRODUCT = 'view.dashboard.analytic.highestRatedProduct',
  VIEW_DASHBOARD_ANALYTIC_MOST_VIEWED_POST = 'view.dashboard.analytic.mostViewedPost',
  VIEW_DASHBOARD_ANALYTIC_HIGHEST_RATED_STORE = 'view.dashboard.analytic.highestRatedStore',
  VIEW_DASHBOARD_ANALYTIC_TOP_COMMENTED_POST = 'view.dashboard.analytic.topCommentedPost',
  VIEW_DASHBOARD_ANALYTIC_MOST_LIKED_POST = 'view.dashboard.analytic.mostLikedPost',
}

import {Job} from "@rlanz/bull-queue";
import ZnChatMessage from "#models/zn_chat_message";
import {DateTime} from "luxon";

interface AnnotateChatMessageJobPayload {
  roomId: string
  data : {
    annotatedChatMessageId: string
    content: string
    adminId: string
    sendTime?: string,
  }
}

export default class AnnotatedChatMessageJob extends Job {
  static get $$filepath() {
    return import.meta.url
  }
  async handle( {roomId, data} : AnnotateChatMessageJobPayload) {
    console.log(data as any)
    await ZnChatMessage.create({
      roomId: roomId,
      adminId: data.adminId,
      content: data.content,
      createdAt: data.sendTime ? DateTime.fromISO(data.sendTime) : undefined,
      annotatedChatMessageId: data.annotatedChatMessageId

    })
  }

  async rescue() {}


}

// Test script for Salon Construction Service API
import axios from 'axios'

const BASE_URL = 'http://localhost:3333/v1'

// Test data
const testData = {
  fullName: '<PERSON>',
  businessName: 'Elegant Nails Salon',
  salonAddress: '123 Main Street, City, State 12345',
  phoneNumber: '+1234567890',
  emailAddress: '<EMAIL>',
  serviceInterest: ['full_renovation', 'interior_design'],
  preferredStartDate: '2025-08-01',
  budgetRange: '$100-250K',
  additionalNotes: 'Looking for a modern, elegant design that will impress our clients.',
  consentConfirmed: true,
  signature: '<PERSON>',
  signatureDate: '2025-07-16',
}

async function testSalonConstructionServiceAPI() {
  try {
    console.log('🧪 Testing Salon Construction Service API...\n')

    // Test 1: Submit form
    console.log('1. Testing form submission...')
    const response = await axios.post(`${BASE_URL}/salon-construction-service`, testData)

    if (response.status === 201) {
      console.log('✅ Form submission successful!')
      console.log('Response:', response.data)

      const signupId = response.data.data.id
      console.log('Signup ID:', signupId)

      // Test 2: Get signup details
      console.log('\n2. Testing get signup details...')
      const getResponse = await axios.get(`${BASE_URL}/salon-construction-service/${signupId}`)

      if (getResponse.status === 200) {
        console.log('✅ Get signup details successful!')
        console.log('Signup data:', getResponse.data.data)
      }

      // Test 3: Try to download PDF (might not be ready yet)
      console.log('\n3. Testing PDF download...')
      try {
        const pdfResponse = await axios.get(
          `${BASE_URL}/salon-construction-service/${signupId}/pdf`
        )
        console.log('✅ PDF download successful!')
        console.log('PDF URL:', pdfResponse.request.res.responseUrl)
      } catch (pdfError) {
        console.log('⚠️ PDF not ready yet or error:', pdfError.response?.data || pdfError.message)
      }
    } else {
      console.log('❌ Form submission failed')
      console.log('Response:', response.data)
    }
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message)
  }
}

// Run the test
testSalonConstructionServiceAPI()

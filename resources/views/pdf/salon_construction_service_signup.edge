<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Salon Construction Service Sign-Up</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 40px;
            background-color: #ffffff;
            color: #333;
            line-height: 1.6;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }
        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            border-radius: 50%;
            background-color: #333;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }
        .title {
            font-size: 28px;
            font-weight: bold;
            margin: 0 0 10px 0;
        }
        .subtitle {
            font-size: 16px;
            color: #666;
            margin: 0;
        }
        .section {
            margin-bottom: 30px;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .field-group {
            margin-bottom: 15px;
        }
        .field-label {
            font-weight: bold;
            display: inline-block;
            width: 150px;
            vertical-align: top;
        }
        .field-value {
            display: inline-block;
            border-bottom: 1px solid #333;
            min-width: 300px;
            padding-bottom: 2px;
        }
        .checkbox-group {
            margin-bottom: 10px;
        }
        .checkbox {
            display: inline-block;
            width: 15px;
            height: 15px;
            border: 2px solid #333;
            margin-right: 10px;
            vertical-align: middle;
            position: relative;
        }
        .checkbox.checked::after {
            content: '✓';
            position: absolute;
            top: -2px;
            left: 2px;
            font-size: 12px;
            font-weight: bold;
        }
        .signature-section {
            margin-top: 40px;
            border-top: 1px solid #ccc;
            padding-top: 20px;
        }
        .signature-line {
            border-bottom: 1px solid #333;
            width: 200px;
            height: 30px;
            display: inline-block;
            margin-left: 10px;
        }
        .consent-text {
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 20px;
        }
        .page-break {
            page-break-before: always;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">Z</div>
        <h1 class="title">Salon Construction Service Sign-Up</h1>
        <p class="subtitle">Modernize your space. Impress your clients. Boost your business.</p>
    </div>

    <div class="section">
        <h2 class="section-title">Salon Owner Information</h2>
        <div class="field-group">
            <span class="field-label">Full Name:</span>
            <span class="field-value">{{ data.fullName || '' }}</span>
        </div>
        <div class="field-group">
            <span class="field-label">Business Name:</span>
            <span class="field-value">{{ data.businessName || '' }}</span>
        </div>
        <div class="field-group">
            <span class="field-label">Salon Address:</span>
            <span class="field-value">{{ data.salonAddress || '' }}</span>
        </div>
        <div class="field-group">
            <span class="field-label">Phone Number:</span>
            <span class="field-value">{{ data.phoneNumber || '' }}</span>
        </div>
        <div class="field-group">
            <span class="field-label">Email Address:</span>
            <span class="field-value">{{ data.emailAddress || '' }}</span>
        </div>
    </div>

    <div class="section">
        <h2 class="section-title">Service Interest (Check all that apply)</h2>
        <div class="checkbox-group">
            <span class="checkbox {{ data.serviceInterest && data.serviceInterest.includes('full_renovation') ? 'checked' : '' }}"></span>
            Full Renovation
        </div>
        <div class="checkbox-group">
            <span class="checkbox {{ data.serviceInterest && data.serviceInterest.includes('partial_remodel') ? 'checked' : '' }}"></span>
            Partial Remodel (e.g. chairs, counters, flooring)
        </div>
        <div class="checkbox-group">
            <span class="checkbox {{ data.serviceInterest && data.serviceInterest.includes('new_construction') ? 'checked' : '' }}"></span>
            New Construction
        </div>
        <div class="checkbox-group">
            <span class="checkbox {{ data.serviceInterest && data.serviceInterest.includes('electrical_plumbing') ? 'checked' : '' }}"></span>
            Electrical / Plumbing Updates
        </div>
        <div class="checkbox-group">
            <span class="checkbox {{ data.serviceInterest && data.serviceInterest.includes('interior_design') ? 'checked' : '' }}"></span>
            Interior Design Services
        </div>
    </div>

    <div class="section">
        <h2 class="section-title">Timeline & Budget</h2>
        <div class="field-group">
            <span class="field-label">Preferred Start Date:</span>
            <span class="field-value">{{ data.preferredStartDate || '' }}</span>
        </div>
        <div class="field-group">
            <span class="field-label">Budget Range:</span>
            @if(data.budgetRange === '$30-50K')
                <span class="checkbox checked"></span> $30-50K
                <span class="checkbox"></span> $50-100K
                <span class="checkbox"></span> $100-250K
                <span class="checkbox"></span> $250-500K
                <span class="checkbox"></span> $500K+
            @elseif(data.budgetRange === '$50-100K')
                <span class="checkbox"></span> $30-50K
                <span class="checkbox checked"></span> $50-100K
                <span class="checkbox"></span> $100-250K
                <span class="checkbox"></span> $250-500K
                <span class="checkbox"></span> $500K+
            @elseif(data.budgetRange === '$100-250K')
                <span class="checkbox"></span> $30-50K
                <span class="checkbox"></span> $50-100K
                <span class="checkbox checked"></span> $100-250K
                <span class="checkbox"></span> $250-500K
                <span class="checkbox"></span> $500K+
            @elseif(data.budgetRange === '$250-500K')
                <span class="checkbox"></span> $30-50K
                <span class="checkbox"></span> $50-100K
                <span class="checkbox"></span> $100-250K
                <span class="checkbox checked"></span> $250-500K
                <span class="checkbox"></span> $500K+
            @elseif(data.budgetRange === '$500K+')
                <span class="checkbox"></span> $30-50K
                <span class="checkbox"></span> $50-100K
                <span class="checkbox"></span> $100-250K
                <span class="checkbox"></span> $250-500K
                <span class="checkbox checked"></span> $500K+
            @else
                <span class="checkbox"></span> $30-50K
                <span class="checkbox"></span> $50-100K
                <span class="checkbox"></span> $100-250K
                <span class="checkbox"></span> $250-500K
                <span class="checkbox"></span> $500K+
            @endif
        </div>
    </div>

    <div class="section">
        <h2 class="section-title">Additional Notes / Vision</h2>
        <div style="border: 1px solid #333; min-height: 100px; padding: 10px;">
            {{ data.additionalNotes || '' }}
        </div>
    </div>

    <div class="signature-section">
        <h2 class="section-title">Consent & Signature</h2>
        <p class="consent-text">
            I confirm that I am the salon owner or authorized representative. I understand that completing this form does not obligate me to service but initiates a consultation.
        </p>
        <div class="field-group">
            <span class="field-label">Signature:</span>
            <span class="signature-line">{{ data.signature || '' }}</span>
            <span style="margin-left: 50px;">Date:</span>
            <span class="signature-line">{{ data.signatureDate || '' }}</span>
        </div>
    </div>
</body>
</html>
